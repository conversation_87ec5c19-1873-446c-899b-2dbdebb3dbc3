"""Risk management module with slot-based position sizing."""
import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
import pandas as pd
from trading_bot.core.adx_stop_tightener import calculate_atr, calculate_true_range
from trading_bot.core.utils import normalize_symbol_for_bybit, count_open_positions_and_orders # Import normalize_symbol_for_bybit


@dataclass
class TradingSlot:
    """Represents a trading slot for position management."""
    slot_id: str
    status: str  # 'available', 'reserved', 'occupied'
    trade_id: Optional[str] = None
    symbol: Optional[str] = None
    risk_allocation: float = 0.0
    rr_ratio: Optional[float] = None
    reserved_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None


class RiskManager:
    """
    Risk management system with slot-based position sizing.
    
    This class wraps and extends existing TradeExecutor and PositionManager
    functionality to provide slot-based risk management while maximizing
    code reuse and maintaining compatibility.
    """
    
    def __init__(self, trader, position_manager, config, slot_manager=None):
        """
        Initialize RiskManager with existing components.

        Args:
            trader: TradeExecutor instance (reuses all trading methods)
            position_manager: PositionManager instance (reuses position tracking)
            config: Configuration object with trading settings
            slot_manager: Optional SlotManager instance to use (if None, creates new one)
        """
        self.trader = trader
        self.position_manager = position_manager
        self.config = config
        self.logger = logging.getLogger(__name__)

        # Use provided SlotManager or create new one for consistent slot handling
        if slot_manager is not None:
            self.slot_manager = slot_manager
            self.logger.info("Using shared SlotManager instance")
        else:
            from trading_bot.core.slot_manager import SlotManager
            self.slot_manager = SlotManager(self.trader, self.position_manager.data_agent, self.config)
            self.logger.info("Created new SlotManager instance")

        # DEPRECATED: Legacy slot tracking (keeping for backward compatibility)
        self.max_slots = getattr(config.trading, 'max_concurrent_trades')
        self.slots: Dict[str, TradingSlot] = {}
        self._initialize_slots()
    
    def _initialize_slots(self):
        """Initialize available trading slots."""
        for i in range(self.max_slots):
            slot_id = f"slot_{i+1}"
            self.slots[slot_id] = TradingSlot(
                slot_id=slot_id,
                status='available'
            )
        self.logger.info(f"Initialized {self.max_slots} trading slots")
    
    def get_available_slots(self, positions_data=None, orders_data=None) -> int:
        """
        Get number of available slots using unified SlotManager.

        IMPORTANT: Only counts actual positions and entry orders. TP/SL orders are NOT counted
        as they are attached to positions and don't consume separate slots.

        Args:
            positions_data: Optional batch position data to avoid API calls
            orders_data: Optional batch order data to avoid API calls

        Returns:
            Number of available slots (can be negative if over limit)
        """
        try:
            # Use unified SlotManager for consistent slot counting
            available_slots, slot_details = self.slot_manager.get_available_order_slots()

            # Log detailed breakdown for debugging
            self.logger.info(f"Slot status: {slot_details.get('occupied_slots', 0)}/{slot_details.get('max_concurrent_trades', 5)} occupied, {available_slots} available")
            self.logger.info(f"Breakdown: {slot_details.get('open_positions', 0)} positions, "
                           f"{slot_details.get('current_entry_orders', 0)} entry orders")

            return available_slots

        except Exception as e:
            self.logger.error(f"Error calculating available slots: {e}")
            # Return max slots as fallback to prevent false slot exhaustion
            self.logger.info(f"Returning max available slots ({self.max_slots}) due to exception")
            return self.max_slots

    async def _fetch_exchange_data_validated(self) -> Dict[str, Any]:
        """
        Fetch and validate exchange data for slot calculation.

        Returns:
            Dict with validation results and exchange data
        """
        try:
            # Fetch open positions using pattern from rebuild_trades_from_pnl.py
            positions_response = self.trader.api_manager.get_positions(
                category="linear",
                settleCoin="USDT"
            )

            if not positions_response or positions_response.get("retCode") != 0:
                return {
                    "success": False,
                    "error": f"Failed to fetch positions: {positions_response.get('retMsg', 'Unknown error') if positions_response else 'No response'}",
                    "api_call": "get_positions"
                }

            # Filter to only include positions with size > 0
            positions_list = positions_response.get("result", {}).get("list", [])
            open_positions = [pos for pos in positions_list if float(pos.get('size', 0)) > 0]

            # Fetch open orders and filter out TP/SL orders
            orders_response = self.trader.api_manager.get_open_orders(
                category="linear",
                limit=50
            )

            if not orders_response or orders_response.get("retCode") != 0:
                return {
                    "success": False,
                    "error": f"Failed to fetch orders: {orders_response.get('retMsg', 'Unknown error') if orders_response else 'No response'}",
                    "api_call": "get_open_orders"
                }

            # Filter to only include entry orders (exclude TP/SL orders)
            # Include 'Deactivated' status as it represents cancelled but potentially restartable orders
            orders_list = orders_response.get("result", {}).get("list", [])
            entry_orders = [
                order for order in orders_list
                if order.get('orderStatus') in ['New', 'PartiallyFilled', 'Deactivated']
                and not order.get('stopOrderType')  # Exclude TP/SL orders
            ]

            # Count TP/SL orders from orders (for informational purposes only)
            tp_sl_orders_from_orders = [
                order for order in orders_list
                if order.get('orderStatus') in ['New', 'PartiallyFilled']
                and order.get('stopOrderType')  # Only TP/SL orders
            ]

            # Count TP/SL from positions (for informational purposes only)
            active_tp_sl_from_positions = 0
            for pos in open_positions:
                has_tp = pos.get('takeProfit') and float(pos.get('takeProfit', 0)) > 0
                has_sl = pos.get('stopLoss') and float(pos.get('stopLoss', 0)) > 0
                if has_tp or has_sl:
                    active_tp_sl_from_positions += 1

            # Total TP/SL count (informational only - not used for slot calculation)
            total_tp_sl_orders = active_tp_sl_from_positions + len(tp_sl_orders_from_orders)

            # Use utility function for consistent counting
            # The utility function will handle API calls internally when trader is provided
            count_result = count_open_positions_and_orders(trader=self.trader)

            # Get the total active trades count
            total_active = count_result.get('active_positions_count', 0) + count_result.get('open_entry_orders_count', 0)

            # Calculate available slots (no max(0, ...) - return raw calculation)
            available_slots = self.max_slots - total_active
            occupied_slots = total_active

            return {
                "success": True,
                "available_slots": available_slots,
                "occupied_slots": occupied_slots,
                "max_slots": self.max_slots,
                "open_positions_count": len(open_positions),
                "entry_orders_count": len(entry_orders),
                "tp_sl_orders_count": total_tp_sl_orders,  # Informational only
                "tp_sl_from_positions": active_tp_sl_from_positions,  # Informational only
                "tp_sl_from_orders": len(tp_sl_orders_from_orders),  # Informational only
                "validation_timestamp": datetime.now(timezone.utc).isoformat(),
                "exchange_data": {
                    "positions": open_positions,
                    "entry_orders": entry_orders,
                    "tp_sl_orders": tp_sl_orders_from_orders  # Informational only
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Exception during exchange data validation: {str(e)}",
                "api_call": "validation_exception"
            }

    async def get_available_slots_with_retry(self, max_retries: int = 3, base_delay: float = 10.0) -> Dict[str, Any]:
        """
        Get available slots using unified SlotManager with retry logic.
        This ensures consistency across the entire codebase.
        """
        retry_info = {
            "attempts_made": 0,
            "total_delay": 0.0,
            "final_attempt_success": False
        }

        for attempt in range(max_retries + 1):  # 0, 1, 2, 3 (total 4 attempts)
            retry_info["attempts_made"] = attempt + 1

            try:
                self.logger.info(f"🔍 Slot validation attempt {attempt + 1}/{max_retries + 1}")

                # Use unified SlotManager for consistent slot counting
                available_slots, slot_details = self.slot_manager.get_available_order_slots()

                # Get detailed breakdown for logging
                occupied_slots = slot_details.get('occupied_slots', 0)
                active_positions = slot_details.get('open_positions', 0)
                entry_orders = slot_details.get('current_entry_orders', 0)
                max_slots = slot_details.get('max_concurrent_trades', self.max_slots)

                retry_info["final_attempt_success"] = True

                result = {
                    "success": True,
                    "available_slots": available_slots,
                    "occupied_slots": occupied_slots,
                    "max_slots": max_slots,
                    "open_positions_count": active_positions,
                    "entry_orders_count": entry_orders,
                    "tp_sl_orders_count": 0,  # SlotManager doesn't track TP/SL separately
                    "total_active_trades": occupied_slots,
                    "retry_info": retry_info,
                    "validation_timestamp": datetime.now(timezone.utc).isoformat()
                }

                self.logger.info(f"✅ Slot validation successful on attempt {attempt + 1}")
                self.logger.info(f"📊 Available slots: {available_slots}/{max_slots}")
                self.logger.info(f"📊 Breakdown: {active_positions} positions, {entry_orders} entry orders")

                return result

            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"❌ API error on attempt {attempt + 1}: {error_msg}")

                if attempt < max_retries:
                    delay = base_delay * (1.5 ** attempt)
                    retry_info["total_delay"] += delay

                    self.logger.warning(f"🔄 Retrying in {delay:.1f} seconds...")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"❌ All {max_retries + 1} slot validation attempts failed: {error_msg}")

                    # IMMEDIATE FAILURE - NO MORE RETRIES OR FALLBACKS
                    self.logger.error(f"🛑 CRITICAL: Slot validation completely failed - cannot proceed safely")
                    return {
                        "success": False,
                        "available_slots": 0,
                        "occupied_slots": self.max_slots,
                        "max_slots": self.max_slots,
                        "error": f"API failure: {error_msg}",
                        "retry_info": retry_info,
                        "validation_timestamp": datetime.now(timezone.utc).isoformat()
                    }

        # This should never be reached, but added for completeness
        self.logger.error(f"🛑 UNEXPECTED: Reached end of retry loop without returning")
        return {
            "success": False,
            "available_slots": 0,
            "occupied_slots": self.max_slots,
            "max_slots": self.max_slots,
            "error": "Unexpected end of retry loop",
            "retry_info": retry_info,
            "validation_timestamp": datetime.now(timezone.utc).isoformat()
        }

    def calculate_rr_weighted_risk_allocation(self, trade_rr: float, active_trades_rr: List[float]) -> float:
        """
        Calculate risk allocation weighted by risk-reward ratio.

        Args:
            trade_rr: Risk-reward ratio for the new trade
            active_trades_rr: List of RR ratios for currently active trades

        Returns:
            Risk allocation multiplier (1.0 = base allocation)
        """
        # Check if RR weighting is enabled
        rr_weighting_enabled = getattr(self.config.trading.position_sizing.rr_weighting, 'enabled')
        if not rr_weighting_enabled:
            return 1.0

        if not active_trades_rr:
            return 1.0

        # Calculate average RR of active trades
        avg_rr = sum(active_trades_rr) / len(active_trades_rr)

        if avg_rr <= 0:
            return 1.0

        # Weight based on RR ratio relative to average
        rr_weight = trade_rr / avg_rr

        # Read constraints from configuration
        min_weight = getattr(self.config.trading.position_sizing.rr_weighting, 'min_weight')
        max_weight = getattr(self.config.trading.position_sizing.rr_weighting, 'max_weight')

        return max(min_weight, min(rr_weight, max_weight))
    
    def calculate_slot_based_position_size(self, entry_price: float, stop_loss: float,
                                         symbol: str, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate position size using TRUE EQUAL RISK ALLOCATION.

        This method ensures each position loses exactly the same risk amount when stop loss is hit:
        - Total portfolio risk = account_balance * risk_percentage
        - Risk per slot = total_risk / available_slots
        - Position size = risk_per_slot / abs(entry_price - stop_loss)
        - max_loss_usd serves as additional safety filter

        Process:
        1. Calculate total available balance and risk amount
        2. Check minimum balance threshold
        3. Divide total risk equally across available slots
        4. Apply max_loss_usd as additional safety cap per position
        5. Calculate position size to achieve exact risk control
        6. Apply exchange limits and precision

        Args:
            entry_price: Entry price for the trade
            stop_loss: Stop loss price
            symbol: Trading symbol
            signal: Trade signal data containing RR info

        Returns:
            Enhanced position size calculation with slot information
        """
        try:
            # 1. Get current slot status and balance
            available_slots = self.get_available_slots()
            count_result = count_open_positions_and_orders(trader=self.trader)
            current_active_trades = count_result.get('active_positions_count', 0) + count_result.get('open_entry_orders_count', 0)

            # 2. Get balance and check minimum threshold
            balance_response = self.trader.get_wallet_balance("USDT")
            if "error" in balance_response:
                return {"error": f"Failed to get wallet balance: {balance_response['error']}"}

            # Extract USDT balance
            result = balance_response.get("result", {})
            coin_list = result.get("list", [])
            usdt_balance = 0.0

            for account in coin_list:
                total_available_balance = float(account.get("totalAvailableBalance", 0))
                if total_available_balance > 0:
                    usdt_balance = total_available_balance
                    break

            # 3. Check minimum balance threshold
            min_balance_threshold = getattr(self.config.trading.position_sizing, 'min_balance_threshold')
            if usdt_balance < min_balance_threshold:
                self.logger.warning(f"⚠️ BALANCE TOO LOW: ${usdt_balance:.2f} < ${min_balance_threshold:.2f} minimum")
                return {
                    "error": f"Insufficient balance for trading. Current: ${usdt_balance:.2f}, "
                            f"Required: ${min_balance_threshold:.2f}"
                }

            # 4. Calculate equal allocation per slot
            total_risk_percentage = getattr(self.config.trading, 'risk_percentage')
            total_risk_amount = usdt_balance * total_risk_percentage

            # Apply USD hard cap
            max_loss_usd = getattr(self.config.trading, 'max_loss_usd')
            if total_risk_amount > max_loss_usd:
                total_risk_amount = max_loss_usd
                self.logger.info(f"Total risk capped at ${max_loss_usd} USD")

            # TRUE EQUAL RISK ALLOCATION: Each position loses exactly the same risk amount when SL hit
            if available_slots > 0:
                # Calculate base risk per slot
                base_risk_per_slot = total_risk_amount / available_slots

                # Apply max_loss_usd as additional safety filter
                risk_per_slot = min(base_risk_per_slot, max_loss_usd)

                if risk_per_slot < base_risk_per_slot:
                    self.logger.info(f"⚠️ RISK CAP APPLIED: Slot risk reduced from ${base_risk_per_slot:.2f} to ${risk_per_slot:.2f} (max_loss_usd limit)")

                self.logger.info(f"🎯 EQUAL RISK ALLOCATION: ${total_risk_amount:.2f} total risk ÷ {available_slots} slots = ${risk_per_slot:.2f} per slot")
                self.logger.info(f"   📊 Each position will lose exactly ${risk_per_slot:.2f} when stop loss is hit")
            else:
                return {"error": "No available slots for new trades"}

            # Calculate position size to achieve exact risk_per_slot
            price_diff = abs(entry_price - stop_loss)
            if price_diff <= 0:
                return {"error": "Invalid entry price or stop loss"}

            # Position size = risk per slot / price difference (ensures exact risk control)
            position_size = risk_per_slot / price_diff
            position_value_per_slot = position_size * entry_price  # Position value will vary based on symbol

            # 6. Apply exchange limits and precision
            position_result = self._apply_exchange_limits_and_precision(
                position_size, entry_price, symbol, risk_per_slot
            )

            if "error" in position_result:
                return position_result

            # 7. Check minimum position value
            final_position_value = position_result.get("position_value", 0)
            min_position_value = getattr(self.config.trading.position_sizing, 'min_position_value_usd', 50.0)

            if final_position_value < min_position_value:
                self.logger.warning(f"⚠️ POSITION TOO SMALL: ${final_position_value:.2f} < ${min_position_value:.2f} minimum")
                return {
                    "error": f"Calculated position too small: ${final_position_value:.2f} < ${min_position_value:.2f} minimum"
                }



            # 8. Enhanced logging for equal risk allocation
            self.logger.info(f"📊 EQUAL RISK ALLOCATION: {symbol}")
            self.logger.info(f"   Total Balance: ${usdt_balance:.2f}")
            self.logger.info(f"   Total Risk: ${total_risk_amount:.2f} ({total_risk_percentage*100:.1f}%)")
            self.logger.info(f"   Available Slots: {available_slots}/{self.max_slots}")
            self.logger.info(f"   Risk per Slot: ${risk_per_slot:.2f}")
            self.logger.info(f"   Position Value per Slot: ${position_value_per_slot:.2f}")
            final_position_value = position_result.get("position_value", 0)
            self.logger.info(f"   Position Size: {position_result.get('position_size', 0):.6f} {symbol.replace('USDT', '')}")
            self.logger.info(f"   Position Value: ${final_position_value:.2f}")
            self.logger.info(f"   ✅ EQUAL RISK ALLOCATION ACHIEVED")

            # 9. Add slot information to result
            position_result.update({
                "slot_info": {
                    "available_slots": available_slots,
                    "max_slots": self.max_slots,
                    "total_risk_amount": total_risk_amount,
                    "risk_per_slot": risk_per_slot,
                    "position_value_per_slot": position_value_per_slot,
                    "allocation_method": "equal_risk",
                    "equal_allocation_base": False,
                    "min_balance_threshold": min_balance_threshold,
                    "min_position_value": min_position_value
                }
            })

            return position_result

        except Exception as e:
            self.logger.error(f"Error in equal slot-based position sizing: {e}")
            return {"error": f"Equal slot-based position sizing failed: {str(e)}"}

    def _apply_exchange_limits_and_precision(self, position_size: float, entry_price: float,
                                           symbol: str, risk_amount: float) -> Dict[str, Any]:
        """
        Apply exchange limits, precision requirements, and calculate final position details.

        Args:
            position_size: Calculated position size
            entry_price: Entry price
            symbol: Trading symbol
            risk_amount: Risk amount for this position

        Returns:
            Dict with validated position size and details
        """
        try:
            # Get instrument info for limits
            instrument_info = self.trader.get_instruments_info(symbol)
            if "error" in instrument_info:
                return {"error": f"Failed to get instrument info: {instrument_info['error']}"}

            # Extract limits
            instrument_data = instrument_info.get("result", {}).get("list", [])
            if not instrument_data:
                return {"error": f"No instrument data found for {symbol}"}

            instrument = instrument_data[0]
            lot_size_filter = instrument.get("lotSizeFilter", {})

            min_order_qty = float(lot_size_filter.get("minOrderQty", 0))
            max_order_qty = float(lot_size_filter.get("maxOrderQty", float('inf')))
            qty_step = float(lot_size_filter.get("qtyStep", 0.000001))

            # Apply precision using Decimal
            from decimal import Decimal, ROUND_DOWN
            position_size_decimal = Decimal(str(position_size))
            qty_step_decimal = Decimal(str(qty_step))

            # Round down to step size
            steps = (position_size_decimal / qty_step_decimal).quantize(Decimal('1'), rounding=ROUND_DOWN)
            position_size_precise = steps * qty_step_decimal
            position_size = float(position_size_precise)

            # Validate limits
            if position_size < min_order_qty:
                return {"error": f"Position size {position_size:.8f} below minimum {min_order_qty}"}

            if position_size > max_order_qty:
                return {"error": f"Position size {position_size:.8f} exceeds maximum {max_order_qty}"}

            # Calculate final position value and risk
            position_value = position_size * entry_price
            actual_risk = risk_amount  # Use the actual risk amount passed from caller

            return {
                "success": True,
                "position_size": position_size,
                "position_value": position_value,
                "actual_risk": actual_risk,
                "min_order_qty": min_order_qty,
                "max_order_qty": max_order_qty,
                "qty_step": qty_step
            }

        except Exception as e:
            return {"error": f"Failed to apply exchange limits: {str(e)}"}
    
    def _get_active_trades_rr(self) -> List[float]:
        """
        Get risk-reward ratios of currently active trades.
        
        Reuses existing position tracking from PositionManager.
        
        Returns:
            List of RR ratios for active trades
        """
        try:
            # Use existing method to get open trades
            open_trades = self.position_manager.data_agent.get_trades(status='open')
            
            rr_ratios = []
            for trade in open_trades:
                # Try to get stored RR ratio or calculate it
                stored_rr = trade.get('risk_reward_ratio')
                if stored_rr and stored_rr > 0:
                    rr_ratios.append(float(stored_rr))
                else:
                    # Calculate RR if not stored
                    entry_price = trade.get('entry_price')
                    take_profit = trade.get('take_profit')
                    stop_loss = trade.get('stop_loss')
                    side = trade.get('side', 'Buy')
                    
                    if all([entry_price, take_profit, stop_loss]):
                        direction = "LONG" if side == "Buy" else "SHORT"
                        rr = self.trader.calculate_risk_reward_ratio(
                            float(entry_price), float(take_profit), 
                            float(stop_loss), direction
                        )
                        if rr > 0:
                            rr_ratios.append(rr)
            
            return rr_ratios
            
        except Exception as e:
            self.logger.error(f"Error getting active trades RR: {e}")
            return []
    
    def can_execute_trade(self, signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Check if a trade can be executed based on slot availability.

        Reuses existing trade scoring and comparison methods.

        Args:
            signal: Trade signal data

        Returns:
            Dict with execution decision and reasoning
        """
        try:
            symbol = signal.get("symbol", "Unknown")
            available_slots = self.get_available_slots()

            # Enhanced logging for slot validation decisions
            self.logger.info(f"🔍 SLOT VALIDATION REQUEST: {symbol}")
            self.logger.info(f"   📊 Current slot status: {available_slots}/{self.max_slots} available")

            if available_slots > 0:
                self.logger.info(f"   ✅ DECISION: ALLOW - {available_slots} slots available")
                return {
                    "can_execute": True,
                    "reason": f"Slot available ({available_slots}/{self.max_slots})",
                    "available_slots": available_slots
                }
            
            # No slots available - check if we should replace an existing trade
            # This reuses the existing compare_trades method
            self.logger.warning(f"   🚫 NO SLOTS AVAILABLE: {self.max_slots}/{self.max_slots} occupied")
            self.logger.info(f"   🔄 CHECKING TRADE REPLACEMENT: {symbol}")

            symbol = signal.get("symbol")
            if not symbol:
                self.logger.error(f"   ❌ REJECTED: No symbol provided for comparison")
                return {
                    "can_execute": False,
                    "reason": "No slots available and no symbol provided for comparison",
                    "available_slots": 0
                }

            # Use utility function to get current active trades count for this symbol
            count_result = count_open_positions_and_orders(trader=self.trader)

            # Check if there are active trades for this symbol
            symbol_active_trades = count_result.get('active_positions_count', 0) + count_result.get('open_entry_orders_count', 0)

            if symbol_active_trades > 0:
                # There are active trades for this symbol - check if we should replace
                # For now, we'll allow replacement if there are active trades (simplified logic)
                self.logger.info(f"   ✅ TRADE REPLACEMENT APPROVED: Active trades found for {symbol} ({symbol_active_trades} trades)")
                return {
                    "can_execute": True,
                    "reason": f"Replace existing trade for {symbol} ({symbol_active_trades} active trades)",
                    "available_slots": 0,
                    "replacement_allowed": True
                }
            else:
                self.logger.info(f"   ❌ TRADE REPLACEMENT NOT NEEDED: No active trades for {symbol}")

            self.logger.error(f"   ❌ FINAL DECISION: REJECT - No slots available and no suitable replacement")
            return {
                "can_execute": False,
                "reason": f"No slots available ({self.max_slots}/{self.max_slots} occupied)",
                "available_slots": 0
            }
            
        except Exception as e:
            self.logger.error(f"❌ CRITICAL ERROR in slot validation for {symbol}: {e}")
            self.logger.error(f"   📊 Emergency fallback: Rejecting trade to prevent over-trading")
            # Emergency fallback: reject trade if we can't validate slots
            return {
                "can_execute": False,
                "reason": f"Slot validation error: {str(e)} - Emergency rejection to prevent over-trading",
                "available_slots": 0,
                "error": str(e),
                "emergency_fallback": True
            }
    
    def reserve_slot_for_trade(self, trade_data: Dict[str, Any]) -> bool:
        """
        Reserve a slot for a trade.
        
        Extends existing record_trade functionality with slot reservation.
        
        Args:
            trade_data: Trade data to record
            
        Returns:
            True if slot reserved successfully
        """
        try:
            # First record the trade using ensure_trade_record method
            result = self.position_manager.ensure_trade_record(
                order_id=trade_data.get('order_id', ''),
                recommendation_id=trade_data.get('recommendation_id', ''),
                symbol=trade_data.get('symbol', ''),
                exchange_data={
                    "symbol": trade_data.get('symbol', ''),
                    "side": trade_data.get('side', 'Buy'),
                    "qty": trade_data.get('qty', 0),
                    "price": trade_data.get('entry_price', 0),
                    "orderStatus": "NEW"  # Assuming this is a new trade
                }
            )
            success = result.get("created", False) or result.get("action") == "found_existing"
            
            if success:
                # Update slot tracking
                trade_id = trade_data.get('id') or result.get("trade", {}).get("trade_id")
                symbol = trade_data.get('symbol')
                
                # Find an available slot and reserve it
                for slot in self.slots.values():
                    if slot.status == 'available':
                        slot.status = 'reserved'
                        slot.trade_id = trade_id
                        slot.symbol = symbol
                        slot.reserved_at = datetime.now(timezone.utc)
                        
                        self.logger.info(f"Reserved slot {slot.slot_id} for trade {trade_id} ({symbol})")
                        return True
                
                self.logger.warning(f"Trade recorded but no slot available to reserve for {trade_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error reserving slot for trade: {e}")
            return False
    
    def release_slot_for_trade(self, trade_id: str) -> bool:
        """
        Release a slot when a trade is closed or cancelled.
        
        Args:
            trade_id: ID of the trade to release slot for
            
        Returns:
            True if slot released successfully
        """
        try:
            for slot in self.slots.values():
                if slot.trade_id == trade_id:
                    slot.status = 'available'
                    slot.trade_id = None
                    slot.symbol = None
                    slot.risk_allocation = 0.0
                    slot.rr_ratio = None
                    slot.reserved_at = None
                    slot.filled_at = None
                    
                    self.logger.info(f"Released slot {slot.slot_id} for trade {trade_id}")
                    return True
            
            self.logger.warning(f"No slot found for trade {trade_id}")
            return False
            
        except Exception as e:
            self.logger.error(f"Error releasing slot for trade {trade_id}: {e}")
            return False
    
    def get_slot_status(self) -> Dict[str, Any]:
        """
        Get comprehensive slot status information.
        
        Returns:
            Dict with detailed slot status
        """
        try:
            available_slots = self.get_available_slots()
            
            slot_details = []
            for slot in self.slots.values():
                slot_details.append({
                    "slot_id": slot.slot_id,
                    "status": slot.status,
                    "trade_id": slot.trade_id,
                    "symbol": slot.symbol,
                    "reserved_at": slot.reserved_at.isoformat() if slot.reserved_at else None
                })
            
            return {
                "max_slots": self.max_slots,
                "available_slots": available_slots,
                "occupied_slots": self.max_slots - available_slots,
                "slot_details": slot_details,
                "utilization_percentage": ((self.max_slots - available_slots) / self.max_slots) * 100
            }
            
        except Exception as e:
            self.logger.error(f"Error getting slot status: {e}")
            return {"error": str(e)}
    
    def validate_slot_consistency(self) -> Dict[str, Any]:
        """
        Validate slot consistency with actual positions and orders.
        
        Uses existing position and order tracking methods.
        
        Returns:
            Validation result with any inconsistencies found
        """
        try:
            # Use utility function for consistent counting
            count_result = count_open_positions_and_orders(trader=self.trader)

            # Get counts from utility function
            actual_occupied = count_result.get('active_positions_count', 0) + count_result.get('open_entry_orders_count', 0)
            tracked_occupied = len([slot for slot in self.slots.values() if slot.status != 'available'])

            inconsistencies = []
            if actual_occupied != tracked_occupied:
                inconsistencies.append(f"Slot tracking mismatch: {tracked_occupied} tracked vs {actual_occupied} actual")

            return {
                "consistent": len(inconsistencies) == 0,
                "actual_occupied_slots": actual_occupied,
                "tracked_occupied_slots": tracked_occupied,
                "inconsistencies": inconsistencies,
                "active_positions": count_result.get('active_positions_count', 0),
                "pending_orders": count_result.get('open_entry_orders_count', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Error validating slot consistency: {e}")
            return {"error": str(e)}
    
    def _get_atr_for_symbol(self, symbol: str, timeframe: str = "60", period: int = 14) -> Optional[float]:
        """
        Get ATR for a symbol using existing ADX module functions.
        
        Args:
            symbol: Trading symbol
            timeframe: Timeframe for ATR calculation (default: "60" for 1h)
            period: ATR period (default: 14)
            
        Returns:
            ATR value as percentage of current price, or None if calculation fails
        """
        try:
            # Normalize the symbol before making the API call
            normalized_symbol = normalize_symbol_for_bybit(symbol)

            # Fetch kline data for ATR calculation
            response = self.trader.api_manager.get_kline(
                category="linear",
                symbol=normalized_symbol, # Use normalized symbol
                interval=timeframe,
                limit=period + 5  # Need extra candles for ATR calculation
            )
            
            if not response or response.get("retCode") != 0:
                self.logger.warning(f"Could not get klines for ATR calculation for {normalized_symbol}")
                return None
            
            klines = response["result"]["list"]
            if len(klines) < period + 1:
                self.logger.warning(f"Insufficient kline data for ATR calculation for {symbol}")
                return None
            
            # Convert klines to DataFrame format for ATR calculation
            candles = []
            for kline in klines:
                candles.append({
                    'high': float(kline[2]),
                    'low': float(kline[3]),
                    'close': float(kline[4])
                })
            
            # Calculate ATR using existing function
            atr_value = calculate_atr(candles, period=period)
            if atr_value is None or atr_value <= 0:
                return None
            
            # Get current price for percentage calculation
            current_price = float(klines[-1][4])  # Close price of last candle
            if current_price <= 0:
                return atr_value  # Return raw ATR if no current price
            
            # Return ATR as percentage of current price
            atr_percentage = (atr_value / current_price) * 100
            return atr_percentage
            
        except Exception as e:
            self.logger.error(f"Error calculating ATR for {symbol}: {e}")
            return None
    
    def calculate_enhanced_position_size_with_safety(self, entry_price: float, stop_loss: float,
                                                   symbol: str, signal: Dict[str, Any],
                                                   risk_percentage_override: Optional[float] = None) -> Dict[str, Any]:
        """
        Enhanced position sizing with equal risk allocation, confidence weighting, and ATR-based volatility adjustment.
        
        This method provides:
        1. Equal base risk allocation per slot (maintaining existing safety parameters)
        2. Confidence-based weighting (0.7-0.85 threshold with 0.8-1.2 multipliers)
        3. ATR-based volatility weighting (using existing ADX module)
        4. Compact logging in your preferred format
        
        Args:
            entry_price: Entry price for the trade
            stop_loss: Stop loss price
            symbol: Trading symbol
            signal: Trade signal containing confidence and other data
            
        Returns:
            Enhanced position size calculation with detailed breakdown
        """
        try:
            # Get configuration settings
            position_sizing_config = getattr(self.config.trading, 'position_sizing', None)

            # Confidence weighting configuration
            confidence_config = getattr(position_sizing_config, 'confidence_weighting', None) if position_sizing_config else None
            confidence_enabled = getattr(confidence_config, 'enabled', True) if confidence_config else True
            low_conf_threshold = getattr(confidence_config, 'low_confidence_threshold', 0.70) if confidence_config else 0.70
            high_conf_threshold = getattr(confidence_config, 'high_confidence_threshold', 0.85) if confidence_config else 0.85
            low_conf_weight = getattr(confidence_config, 'low_confidence_weight', 0.8) if confidence_config else 0.8
            neutral_conf_weight = getattr(confidence_config, 'neutral_confidence_weight', 1.0) if confidence_config else 1.0
            high_conf_weight = getattr(confidence_config, 'high_confidence_weight', 1.2) if confidence_config else 1.2

            # Volatility weighting configuration
            volatility_config = getattr(position_sizing_config, 'volatility_weighting', None) if position_sizing_config else None
            volatility_enabled = getattr(volatility_config, 'enabled', True) if volatility_config else True
            use_atr_based = getattr(volatility_config, 'use_atr_based', True) if volatility_config else True
            low_volatility_weight = getattr(volatility_config, 'low_volatility_weight', 1.2) if volatility_config else 1.2
            high_volatility_weight = getattr(volatility_config, 'high_volatility_weight', 0.7) if volatility_config else 0.7
            atr_low_threshold = getattr(volatility_config, 'atr_low_threshold', 0.02) if volatility_config else 0.02
            atr_high_threshold = getattr(volatility_config, 'atr_high_threshold', 0.05) if volatility_config else 0.05

            # Combined weight limits
            max_combined_weight = getattr(position_sizing_config, 'max_combined_weight', 1.5) if position_sizing_config else 1.5
            min_combined_weight = getattr(position_sizing_config, 'min_combined_weight', 0.6) if position_sizing_config else 0.6
            
            # Get signal data
            confidence = signal.get('confidence', 0.5)
            direction = signal.get("direction", signal.get("recommendation", "LONG"))
            take_profit = signal.get("take_profit", entry_price * 1.02)
            
            # Calculate base RR ratio
            base_rr = self.trader.calculate_risk_reward_ratio(
                entry_price, take_profit, stop_loss, direction, symbol
            )
            
            # 1. Calculate base risk allocation (equal per slot)
            # Use override if provided, otherwise use config
            base_risk_per_trade = risk_percentage_override if risk_percentage_override is not None else \
                                  getattr(self.config.trading, 'risk_percentage', 0.01)
            
            # 2. Apply confidence weighting
            confidence_weight = neutral_conf_weight  # Default
            if confidence_enabled:
                if confidence <= low_conf_threshold:
                    confidence_weight = low_conf_weight
                elif confidence >= high_conf_threshold:
                    confidence_weight = high_conf_weight
                else:
                    # Linear interpolation for medium confidence
                    confidence_weight = low_conf_weight + (
                        (confidence - low_conf_threshold) / (high_conf_threshold - low_conf_threshold)
                    ) * (high_conf_weight - low_conf_weight)
            
            # 3. Apply ATR-based volatility weighting
            volatility_weight = 1.0  # Default neutral
            if volatility_enabled and use_atr_based:
                atr_percentage = self._get_atr_for_symbol(symbol)
                if atr_percentage is not None:
                    if atr_percentage <= atr_low_threshold:
                        volatility_weight = low_volatility_weight  # Low volatility = higher weight
                    elif atr_percentage >= atr_high_threshold:
                        volatility_weight = high_volatility_weight  # High volatility = lower weight
                    else:
                        # Linear interpolation for medium volatility
                        volatility_weight = low_volatility_weight - (
                            (atr_percentage - atr_low_threshold) / (atr_high_threshold - atr_low_threshold)
                        ) * (low_volatility_weight - high_volatility_weight)
            
            # 4. Calculate combined weight with limits
            combined_weight = confidence_weight * volatility_weight
            combined_weight = max(min(combined_weight, max_combined_weight), min_combined_weight)
            
            # 5. Calculate final adjusted risk percentage
            final_risk_percentage = base_risk_per_trade * combined_weight
            
            # 6. Call existing position sizing with adjusted risk
            position_result = self.trader.calculate_position_size(
                entry_price=entry_price,
                stop_loss=stop_loss,
                symbol=symbol,
                risk_percentage=final_risk_percentage
            )
            
            if position_result.get("success"):
                # Add enhanced breakdown information
                position_result.update({
                    "enhanced_sizing": {
                        "base_risk_percentage": base_risk_per_trade * 100,
                        "confidence": confidence,
                        "confidence_weight": confidence_weight,
                        "atr_percentage": self._get_atr_for_symbol(symbol),
                        "volatility_weight": volatility_weight,
                        "combined_weight": combined_weight,
                        "final_risk_percentage": final_risk_percentage * 100,
                        "base_rr_ratio": base_rr
                    }
                })
                
                # Compact logging format (your preferred style)
                confidence_label = "L" if confidence <= low_conf_threshold else "H" if confidence >= high_conf_threshold else "M"
                volatility_label = "L" if (self._get_atr_for_symbol(symbol) or 0) <= atr_low_threshold else "H" if (self._get_atr_for_symbol(symbol) or 0) >= atr_high_threshold else "M"
                
                position_size = position_result.get("position_size", 0)
                position_value = position_result.get("position_value", 0)
                actual_risk = position_result.get("actual_risk", 0)
                
                self.logger.info(f"📊 {symbol:<8} | Conf:{confidence:.2f}({confidence_label}) {confidence_weight:.2f}x | "
                               f"Vol:{self._get_atr_for_symbol(symbol) or 0:.3f}({volatility_label}) {volatility_weight:.2f}x | "
                               f"Total:{combined_weight:.2f}x | "
                               f"${entry_price:.2f}→${take_profit:.2f} | "
                               f"Size:{position_size:.2f} (${position_value:.0f}) ✅")
            
            return position_result
            
        except Exception as e:
            self.logger.error(f"Error in enhanced position sizing: {e}")
            return {"error": f"Enhanced position sizing failed: {str(e)}"}
    
    def print_filter_summary(self) -> None:
        """
        Print a dedicated summary of risk manager filter categories and counts.
        
        This method provides a clear overview of all the filtering mechanisms
        used by the risk manager to evaluate trades.
        """
        try:
            # Use utility function for consistent counting
            count_result = count_open_positions_and_orders(trader=self.trader)

            # Get counts from utility function
            active_positions = count_result.get('active_positions_count', 0)
            pending_orders = count_result.get('open_entry_orders_count', 0)
            tp_sl_orders = count_result.get('take_profit_orders', 0) + count_result.get('stop_loss_orders', 0)
            
            # Get slot information
            slot_status = self.get_slot_status()
            available_slots = slot_status.get("available_slots", 0)
            max_slots = slot_status.get("max_slots", 0)
            occupied_slots = slot_status.get("occupied_slots", 0)
            
            # Get configuration limits
            max_concurrent_trades = getattr(self.config.trading, 'max_concurrent_trades', 3)
            min_confidence = getattr(self.config.trading, 'min_confidence_threshold')
            min_rr = getattr(self.config.trading, 'min_rr', 1.2)
            risk_percentage = getattr(self.config.trading, 'risk_percentage')
            max_loss_usd = getattr(self.config.trading, 'max_loss_usd')
            
            # Print formatted summary
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"📊 RISK MANAGER FILTER SUMMARY")
            self.logger.info(f"{'='*60}")
            
            # Slot-based filters
            print(f"\n🎰 SLOT-BASED FILTERS:")
            print(f"   Maximum Concurrent Trades: {max_slots}")
            print(f"   Currently Occupied Slots: {occupied_slots}")
            print(f"   Available Slots: {available_slots}")
            print(f"   Active Positions: {active_positions}")
            print(f"   Pending Entry Orders: {pending_orders}")
            print(f"   TP/SL Orders: {tp_sl_orders}")
            
            # Risk parameter filters
            print(f"\n🛡️  RISK PARAMETER FILTERS:")
            print(f"   Minimum Confidence Threshold: {min_confidence:.2f}")
            print(f"   Minimum Risk-Reward Ratio: {min_rr:.1f}")
            print(f"   Risk Percentage per Trade: {risk_percentage*100:.1f}%")
            print(f"   Maximum Loss per Trade: ${max_loss_usd:.2f}")
            
            # Calculate current utilization
            current_utilization = ((active_positions + pending_orders) / max_slots * 100) if max_slots > 0 else 0
            print(f"\n📈 CURRENT UTILIZATION:")
            print(f"   Total Active Trades: {active_positions + pending_orders}")
            print(f"   Utilization Rate: {current_utilization:.1f}%")
            
            # Show slot details if any are occupied
            if occupied_slots > 0:
                print(f"\n📋 SLOT DETAILS:")
                for slot in self.slots.values():
                    if slot.status != 'available':
                        status_icon = "🟡" if slot.status == 'reserved' else "🟢"
                        print(f"   {status_icon} Slot {slot.slot_id}: {slot.status.upper()}")
                        if slot.symbol:
                            print(f"      Symbol: {slot.symbol}")
                        if slot.trade_id:
                            print(f"      Trade ID: {slot.trade_id}")
            
            print(f"{'='*60}")
            
        except Exception as e:
            self.logger.error(f"Error printing filter summary: {e}")
            self.logger.error(f"❌ Error generating filter summary: {e}")
